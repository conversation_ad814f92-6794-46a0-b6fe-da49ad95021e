import '../styles/men.css';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Lenis from 'lenis';
import { sharedProducts, findProductById } from './shared-products';
import { initCart, addToCart, showToast } from './cart';


// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Use the shared products data
const products = sharedProducts;

const lenis = new Lenis();

// Use requestAnimationFrame to continuously update the scroll
function raf(time) {
  lenis.raf(time);
  requestAnimationFrame(raf);
}
requestAnimationFrame(raf);

// Initialize cart immediately to ensure it works when navigating between pages
initCart();

// Initialize smooth scrolling
document.addEventListener('DOMContentLoaded', () => {

  // Populate products grid
  const productsGrid = document.querySelector('.products-grid');
  const galleryPriceSection = document.getElementById('gallery-price');

  // Check for product ID in URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const productId = urlParams.get('product');

  // Filter products to show only men's shoes and the Top Picks products
  const mensProducts = products.filter(product =>
    product.gender === 'Men\'s shoe' ||
    product.id === 'p2' || // Nike Dunk Low Retro (already a men's shoe)
    product.id === 'p4' || // Sabrina 2 'Stronger Than Gold' EP (basketball shoe)
    product.id === 'p5'    // Sabrina 2 EP (basketball shoe)
  );

  console.log('Total products:', products.length);
  console.log('Filtered men\'s products:', mensProducts.length);
  console.log('Men\'s products:', mensProducts);

  // Generate product cards
  mensProducts.forEach(product => {
    const productCard = document.createElement('div');
    productCard.className = 'product-card';
    productCard.dataset.productId = product.id;

    productCard.innerHTML = `
      <div class="product-image">
        <img src="${product.image}" alt="${product.name}">
      </div>
      <div class="product-info">
        <h3 class="product-name">${product.name}</h3>
        <div class="product-price">
          <span class="original-price">₹${product.originalPrice}</span>
          <span class="current-price">₹${product.currentPrice}</span>
          <span class="discount-badge">-${product.discount}%</span>
        </div>
      </div>
    `;

    // Add click event to entire card to show product details
    productCard.addEventListener('click', (e) => {
      // Prevent default behavior
      e.preventDefault();

      console.log('Product card clicked, showing details for:', product.name);
      showProductDetails(product);
    }, { passive: false });

    productsGrid.appendChild(productCard);
  });

  // Function to show product details
  function showProductDetails(product) {
    // Update product details content
    const mainProductImage = document.getElementById('main-product-image');
    mainProductImage.src = product.image;
    mainProductImage.alt = product.name;

    // Add product ID as data attribute for more reliable identification
    mainProductImage.dataset.productId = product.id;

    console.log('Setting product details for:', product.name, 'ID:', product.id);

    // Update thumbnails to show the selected product
    const thumbnails = document.querySelectorAll('.thumbnails.vertical .thumbnail');
    thumbnails.forEach(thumbnail => {
      thumbnail.classList.remove('active');
      if (thumbnail.src === product.image) {
        thumbnail.classList.add('active');
      }

      // Also add product ID to thumbnails for consistency
      thumbnail.dataset.productId = product.id;
    });

    // If no thumbnail matches the product image, set the first one as active
    const activeThumb = document.querySelector('.thumbnails.vertical .thumbnail.active');
    if (!activeThumb && thumbnails.length > 0) {
      thumbnails[0].classList.add('active');
    }

    // Update product name
    const productNameElement = document.getElementById('product-name');
    if (productNameElement) {
      productNameElement.textContent = product.name;
    }

    // Update price information
    document.querySelector('.price-info .original-price').textContent = `₹${product.originalPrice}`;
    document.querySelector('.price-info .current-price').textContent = `₹${product.currentPrice}`;
    document.querySelector('.price-info .discount-badge').textContent = `-${product.discount}%`;

    // Update product description
    document.getElementById('product-description-text').textContent = product.description;

    // Update Add to Cart button with product ID
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    if (addToCartBtn) {
      addToCartBtn.dataset.productId = product.id;
    }

    // Show the product details section with animation
    if (galleryPriceSection.style.display === 'none') {
      // First make it visible but with opacity 0
      galleryPriceSection.style.opacity = '0';
      galleryPriceSection.style.display = 'block';

      // Scroll to product details section
      galleryPriceSection.scrollIntoView({ behavior: 'smooth' });

      // Animate the product details section
      gsap.fromTo(galleryPriceSection,
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }
      );
    } else {
      // If already visible, just update with a subtle animation
      gsap.fromTo(galleryPriceSection,
        { opacity: 0.8 },
        { opacity: 1, duration: 0.4, ease: 'power2.out' }
      );

      // Scroll to product details section
      galleryPriceSection.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // Add click event to back button
  const backButton = document.querySelector('.back-to-collection-btn');
  if (backButton) {
    backButton.addEventListener('click', () => {
      // Hide product details section with animation
      gsap.to(galleryPriceSection, {
        opacity: 0,
        y: 50,
        duration: 0.5,
        ease: 'power2.in',
        onComplete: () => {
          galleryPriceSection.style.display = 'none';

          // Scroll back to men's collection
          document.getElementById('mens-collection').scrollIntoView({ behavior: 'smooth' });
        }
      });
    });
  }

  // Men's Collection section animation
  gsap.from('#mens-collection .product-card', {
    y: 50,
    // opacity: 0,
    duration: 0.8,
    // stagger: 0.1,
    ease: 'power4.out'
  });

  // Check if we need to show a specific product from URL parameter
  if (productId) {
    const selectedProduct = findProductById(productId);
    if (selectedProduct) {
      // Show the product details after a short delay to allow animations to complete
      setTimeout(() => {
        showProductDetails(selectedProduct);
      }, 500);
    }
  }

  // Cart modal functionality is now handled by cart.js

  // Initialize cart
  initCart();

  // Log cart status to verify it's working
  console.log('Cart initialized on men\'s page');

  // Note: User icon click event is now handled in auth-ui.js

  // Hero animations
  const heroTl = gsap.timeline();

  heroTl.from('.hero-text h1', {
    y: 50,
    opacity: 0,
    duration: 0.8,
    ease: 'power3.out'
  })
  .from('.hero-text p', {
    y: 30,
    opacity: 0,
    duration: 0.6,
    ease: 'power3.out'
  }, '-=0.4')
  .from('.hero-text .buy-now-btn', {
    y: -10,
    duration: 0.5,
    ease: 'power3.out'
  }, '-=0.3')
  .from('.hero-image img', {
    x: 100,
    duration: 1,
    ease: 'power2.out',
    rotation: 10
  }, '-=0.8');

  // Features section animation
  gsap.from('.feature', {
    y: 50,
    duration: .8,
    stagger: 0.01,
    scrub: 2,
    ease: 'power2.out'
  });

  // Gallery section animation
  gsap.from('.gallery-left', {
    // scrollTrigger: {
    //   trigger: '#gallery-price',
    //   start: 'top 70%',
    //   toggleActions: 'play none none none'
    // },
    x: -50,
    // opacity: 0,
    duration: 0.8,
    scrub: 3,
    ease: 'power2.out'
  });

  gsap.from('.price-container', {
    scrollTrigger: {
      trigger: '#gallery-price',
      start: 'top 70%',
      toggleActions: 'play none none none'
    },
    x: 50,
    opacity: 0,
    duration: 0.8,
    ease: 'power2.out'
  });

  // Reviews section animation
  gsap.from('.review-stats', {
    scrollTrigger: {
      trigger: '#reviews',
      start: 'top 80%',
      toggleActions: 'play none none none'
    },
    y: 30,
    opacity: 0,
    duration: 0.6,
    ease: 'power2.out'
  });

  gsap.from('.review-carousel', {
    scrollTrigger: {
      trigger: '#reviews',
      start: 'top 70%',
      toggleActions: 'play none none none'
    },
    y: 50,
    opacity: 0,
    duration: 0.8,
    ease: 'power2.out',
    delay: 0.2
  });
  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');
      if (targetId !== '#') {
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          window.scrollTo({
            top: targetElement.offsetTop - 80, // Adjust for navbar height
            behavior: 'smooth'
          });
        }
      }
    });
  });

  // Product Gallery Functionality
  const mainImage = document.getElementById('main-product-image');
  const thumbnails = document.querySelectorAll('.thumbnails.vertical .thumbnail');

  thumbnails.forEach(thumbnail => {
    thumbnail.addEventListener('click', function() {
      // Update main image
      mainImage.src = this.src;
      mainImage.alt = this.alt;

      // Preserve product ID when switching thumbnails
      if (this.dataset.productId) {
        mainImage.dataset.productId = this.dataset.productId;
      }

      // Update active thumbnail
      thumbnails.forEach(thumb => thumb.classList.remove('active'));
      this.classList.add('active');

      // Add animation to main image
      gsap.fromTo(mainImage,
        { opacity: 0, scale: 0.95 },
        { opacity: 1, scale: 1, duration: 0.3, ease: 'power2.out' }
      );
    });
  });

  // Size selector functionality
  const sizeButtons = document.querySelectorAll('.size-btn');
  sizeButtons.forEach(button => {
    button.addEventListener('click', function() {
      sizeButtons.forEach(btn => btn.classList.remove('active'));
      this.classList.add('active');
    });
  });

  // Review carousel functionality
  const reviewSlides = document.querySelectorAll('.review-slide');
  const dots = document.querySelectorAll('.dot');
  const prevBtn = document.querySelector('.prev-btn');
  const nextBtn = document.querySelector('.next-btn');
  let currentSlide = 0;

  function showSlide(index) {
    // Hide all slides
    reviewSlides.forEach(slide => {
      slide.classList.remove('active');
    });

    // Remove active class from all dots
    dots.forEach(dot => {
      dot.classList.remove('active');
    });

    // Show the current slide and activate corresponding dot
    reviewSlides[index].classList.add('active');
    dots[index].classList.add('active');
  }

  // Next button click
  nextBtn.addEventListener('click', () => {
    currentSlide = (currentSlide + 1) % reviewSlides.length;
    showSlide(currentSlide);
  });

  // Previous button click
  prevBtn.addEventListener('click', () => {
    currentSlide = (currentSlide - 1 + reviewSlides.length) % reviewSlides.length;
    showSlide(currentSlide);
  });

  // Dot click functionality
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      currentSlide = index;
      showSlide(currentSlide);
    });
  });

  // Auto-advance slides every 5 seconds
  setInterval(() => {
    currentSlide = (currentSlide + 1) % reviewSlides.length;
    showSlide(currentSlide);
  }, 5000);

  // Price animation on hover
  const priceDisplay = document.querySelector('.price-display');
  if (priceDisplay) {
    priceDisplay.addEventListener('mouseenter', () => {
      priceDisplay.style.transform = 'scale(1.05)';
    });

    priceDisplay.addEventListener('mouseleave', () => {
      priceDisplay.style.transform = 'scale(1)';
    });
  }

  // Add to cart button functionality
  const addToCartBtn = document.querySelector('.add-to-cart-btn');
  if (addToCartBtn) {
    addToCartBtn.addEventListener('click', () => {
      // Find the currently displayed product
      const productImage = document.getElementById('main-product-image');

      // Try to get product ID from data attribute first (more reliable)
      const productId = productImage.dataset.productId;
      let product;

      if (productId) {
        // Use the helper function to find product by ID
        product = findProductById(productId);
        console.log('Found product by ID:', productId, product);
      }

      // If no product found by ID, try by name
      if (!product) {
        const productName = productImage.alt;
        product = products.find(p => p.name === productName);
        console.log('Found product by name:', productName, product);
      }

      // If still no product found, try by image source
      if (!product) {
        const productImage = document.getElementById('main-product-image');
        const imageSrc = productImage.src;
        product = products.find(p => p.image === imageSrc);
        console.log('Found product by image:', imageSrc, product);
      }

      if (product) {
        // Add product to cart
        addToCart(product, true); // true to open cart after adding

        // Update button text and style
        addToCartBtn.textContent = 'Added to Cart!';
        addToCartBtn.style.backgroundColor = 'var(--accent-color)';

        setTimeout(() => {
          addToCartBtn.textContent = 'Add to Cart';
          addToCartBtn.style.backgroundColor = 'var(--secondary-color)';
        }, 2000);
      } else {
        console.error('Could not find product to add to cart');
        // Show error toast
        showToast('Error adding product to cart', 'error');
      }
    });
  }

  // Buy now button functionality
  const buyNowBtn = document.querySelector('.buy-now-btn');
  if (buyNowBtn) {
    buyNowBtn.addEventListener('click', () => {
      // Get the product ID from the hero image
      const heroProductId = document.querySelector('#hero .hero-image img').dataset.productId;

      // Find the product in our products array using the product ID
      let heroProduct;

      if (heroProductId) {
        // Use the helper function to find the product by ID
        heroProduct = findProductById(heroProductId);
      }

      // If no product ID or product not found, try to match by name or image
      if (!heroProduct) {
        const heroProductName = document.querySelector('#hero .hero-text h1').textContent;
        const heroProductImage = document.querySelector('#hero .hero-image img').getAttribute('src');

        heroProduct = products.find(product =>
          product.name === heroProductName ||
          product.image === heroProductImage
        );
      }

      // If found, show its details, otherwise fallback to the first product
      if (heroProduct) {
        showProductDetails(heroProduct);
      } else {
        showProductDetails(products[0]);
        console.warn('Hero product not found in products array, showing first product instead');
      }
    });
  }

  // Scroll animations for sections
  const animateOnScroll = () => {
    const sections = document.querySelectorAll('section');

    sections.forEach(section => {
      const sectionTop = section.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (sectionTop < windowHeight * 0.75) {
        section.style.opacity = '1';
        section.style.transform = 'translateY(0)';
      }
    });
  };

  // Apply initial styles for scroll animation
  const sections = document.querySelectorAll('section');
  sections.forEach(section => {
    if (section.id !== 'hero') {
      section.style.opacity = '0';
      section.style.transform = 'translateY(50px)';
      section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    }
  });

  // Listen for scroll events
  window.addEventListener('scroll', animateOnScroll);

  // Trigger once on load
  animateOnScroll();

  // Add event delegation for product card clicks (backup method)
  document.addEventListener('click', (e) => {
    const productCard = e.target.closest('.product-card');
    if (productCard) {
      e.preventDefault();
      const productId = productCard.dataset.productId;
      if (productId) {
        const product = products.find(p => p.id === productId);
        if (product) {
          console.log('Event delegation: Product card clicked, showing details for:', product.name);
          showProductDetails(product);
        }
      }
    }
  });
});

// Navbar scroll feature
function navBarScrollAnimation() {
  let lastScrollTop = 0;
  window.addEventListener("scroll", function() {
    let navbar = document.querySelector("nav");
    let currentScroll = window.pageYOffset;
    if (currentScroll > lastScrollTop && currentScroll > 100) {
      navbar.style.top = "-90px"; // Hide navbar
    } else {
      navbar.style.top = "0"; // Show navbar
    }
    lastScrollTop = currentScroll;
  });
}
navBarScrollAnimation();