// Import shared functionality
import { initCart, addToCart } from './cart';
import { sharedProducts, findProductById } from './shared-products';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Lenis from 'lenis';


const lenis = new Lenis();

// Use requestAnimationFrame to continuously update the scroll
function raf(time) {
  lenis.raf(time);
  requestAnimationFrame(raf);
}
requestAnimationFrame(raf);
// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Filter kids products from shared products
const kidsProducts = sharedProducts.filter(product => product.id.startsWith('k'));

// Use the shared products for kids
// We already added the kids products to shared-products.js, so we don't need fallback data

// Initialize cart immediately to ensure it works when navigating between pages
initCart();

// Function to populate kids products
function populateKidsProducts() {
  const productsGrid = document.querySelector('.products-grid');
  if (!productsGrid) {
    console.error('Products grid not found');
    return;
  }

  console.log('Kids products found:', kidsProducts.length);
  console.log('Kids products:', kidsProducts);

  // Clear existing products
  productsGrid.innerHTML = '';

  // Generate product cards for all kids products
  kidsProducts.forEach(product => {
    const productCard = document.createElement('div');
    productCard.className = 'product-card';
    productCard.dataset.productId = product.id;

    productCard.innerHTML = `
      <div class="product-image">
        <img src="${product.image}" alt="${product.name}" loading="lazy">
        <div class="product-badge">${product.discount}% OFF</div>
      </div>
      <div class="product-info">
        <h3 class="product-name">${product.name}</h3>
        <div class="product-price">
          <span class="original-price">₹${product.originalPrice}</span>
          <span class="current-price">₹${product.currentPrice}</span>
        </div>
      </div>
    `;

    // Add click event to entire card to show product details
    productCard.addEventListener('click', (e) => {
      // Prevent default behavior
      e.preventDefault();

      console.log('Product card clicked, showing details for:', product.name);
      showProductDetails(product.id);
    }, { passive: false });

    productsGrid.appendChild(productCard);
  });
}

// Initialize the page
function initializePage() {
  // Initialize cart functionality
  initCart();

  // Populate the products grid with kids products
  populateKidsProducts();

  // Add event listeners to product cards

  // Product card click handler for showing product details
  document.querySelectorAll('.product-card').forEach(card => {
    card.addEventListener('click', (e) => {
      // Prevent default behavior
      e.preventDefault();

      const productId = card.dataset.productId;
      if (productId) {
        console.log('Product card clicked, showing details for ID:', productId);
        showProductDetails(productId);
      }
    }, { passive: false });
  });

  // Add event listener for "Back to Collection" button
  const backToCollectionBtn = document.querySelector('.back-to-collection-btn');
  if (backToCollectionBtn) {
    backToCollectionBtn.addEventListener('click', () => {
      console.log('Back to collection button clicked');
      const productDetails = document.getElementById('product-details');
      productDetails.classList.remove('active');
      console.log('Removed active class from product details section');

      // Scroll to collection section
      const collectionSection = document.getElementById('kids-collection');
      collectionSection.scrollIntoView({ behavior: 'smooth' });
    });
  }

  // Setup review carousel
  setupReviewCarousel();

  // Setup size selection
  setupSizeSelection();

  // Check URL parameters for product ID
  const urlParams = new URLSearchParams(window.location.search);
  const productId = urlParams.get('product');
  if (productId) {
    showProductDetails(productId);
  }

  // Setup "Shop Now" button in hero section
  const shopNowBtn = document.querySelector('.shop-now-btn');
  if (shopNowBtn) {
    shopNowBtn.addEventListener('click', () => {
      const kidsCollectionSection = document.getElementById('kids-collection');
      kidsCollectionSection.scrollIntoView({ behavior: 'smooth' });
    });
  }
}

// Function to show product details
function showProductDetails(productId) {
  console.log('Showing product details for:', productId);

  // First try to find the product in our shared products
  const product = findProductById(productId);

  // For our manually added product cards, we'll use a simpler approach
  // Get the product card that was clicked
  const productCard = document.querySelector(`.product-card[data-product-id="${productId}"]`);
  if (!productCard && !product) {
    console.error('Product card or product not found for ID:', productId);
    return;
  }

  // Get elements
  const productDetails = document.getElementById('product-details');
  const mainImage = document.getElementById('main-product-image');
  const productTitle = document.querySelector('#product-details .product-title');
  const originalPrice = document.querySelector('#product-details .original-price');
  const currentPrice = document.querySelector('#product-details .current-price');
  const discountBadge = document.querySelector('#product-details .discount-badge');
  const addToCartBtn = document.querySelector('#product-details .add-to-cart-btn');

  // Get data from the product card
  const cardImage = productCard.querySelector('.product-image img');
  const cardName = productCard.querySelector('.product-name');
  const cardOriginalPrice = productCard.querySelector('.original-price');
  const cardCurrentPrice = productCard.querySelector('.current-price');
  const cardBadge = productCard.querySelector('.product-badge');

  // Update product details
  if (cardImage) {
    mainImage.src = cardImage.src;
    mainImage.alt = cardImage.alt;
  }

  if (cardName) {
    productTitle.textContent = cardName.textContent;
  }

  if (cardOriginalPrice) {
    originalPrice.textContent = cardOriginalPrice.textContent;
  }

  if (cardCurrentPrice) {
    currentPrice.textContent = cardCurrentPrice.textContent;
  }

  if (cardBadge) {
    // Extract the percentage from the badge (e.g., "18% OFF" -> "-18%")
    const percentage = cardBadge.textContent.match(/\d+/);
    if (percentage) {
      discountBadge.textContent = `-${percentage[0]}%`;
    }
  }

  // Update "Add to Cart" button
  addToCartBtn.dataset.productId = productId;

  // Update thumbnails - use the same image for all thumbnails for now
  const thumbnails = document.querySelectorAll('.thumbnail');
  thumbnails.forEach((thumbnail, index) => {
    // Remove existing event listeners by cloning and replacing
    const newThumbnail = thumbnail.cloneNode(true);
    thumbnail.parentNode.replaceChild(newThumbnail, thumbnail);

    if (index === 0 && cardImage) {
      newThumbnail.src = cardImage.src;
      newThumbnail.classList.add('active');
    }

    // Add click event to thumbnail
    newThumbnail.addEventListener('click', () => {
      // Remove active class from all thumbnails
      thumbnails.forEach(t => t.classList.remove('active'));

      // Add active class to clicked thumbnail
      newThumbnail.classList.add('active');

      // Update main image
      mainImage.src = newThumbnail.src;
    });
  });

  // Show product details section
  console.log('Adding active class to product details section');
  productDetails.classList.add('active');
  productDetails.scrollIntoView({ behavior: 'smooth' });

  // Update URL with product ID without reloading the page
  const url = new URL(window.location);
  url.searchParams.set('product', productId);
  window.history.pushState({}, '', url);
}

// Function to handle review carousel
function setupReviewCarousel() {
  const slides = document.querySelectorAll('.review-slide');
  const dots = document.querySelectorAll('.carousel-dots .dot');
  const prevBtn = document.querySelector('.prev-btn');
  const nextBtn = document.querySelector('.next-btn');

  if (!slides.length || !dots.length) return;

  let currentSlide = 0;
  const totalSlides = slides.length;

  // Function to show a specific slide
  function showSlide(index) {
    // Hide all slides
    slides.forEach(slide => {
      slide.classList.remove('active');
    });

    // Remove active class from all dots
    dots.forEach(dot => {
      dot.classList.remove('active');
    });

    // Show the current slide and activate the corresponding dot
    slides[index].classList.add('active');
    dots[index].classList.add('active');

    // Update current slide index
    currentSlide = index;
  }

  // Event listener for previous button
  if (prevBtn) {
    prevBtn.addEventListener('click', () => {
      let newIndex = currentSlide - 1;
      if (newIndex < 0) {
        newIndex = totalSlides - 1;
      }
      showSlide(newIndex);
    });
  }

  // Event listener for next button
  if (nextBtn) {
    nextBtn.addEventListener('click', () => {
      let newIndex = currentSlide + 1;
      if (newIndex >= totalSlides) {
        newIndex = 0;
      }
      showSlide(newIndex);
    });
  }

  // Event listeners for dots
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      showSlide(index);
    });
  });

  // Auto-advance slides every 5 seconds
  setInterval(() => {
    let newIndex = currentSlide + 1;
    if (newIndex >= totalSlides) {
      newIndex = 0;
    }
    showSlide(newIndex);
  }, 5000);
}

// Function to handle size selection in product details
function setupSizeSelection() {
  const sizeButtons = document.querySelectorAll('.size-btn');

  sizeButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all size buttons
      sizeButtons.forEach(btn => btn.classList.remove('active'));

      // Add active class to clicked button
      button.classList.add('active');
    });
  });
}

// Add animation to product cards
function animateProductCards() {
  const cards = document.querySelectorAll('.product-card');

  cards.forEach((card, index) => {
    // Add staggered animation delay
    card.style.animationDelay = `${index * 0.1}s`;
    card.classList.add('animate');
  });
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Ensure product details section is hidden by default
  const productDetails = document.getElementById('product-details');
  if (productDetails) {
    productDetails.classList.remove('active');
    console.log('Product details section hidden on page load');
  } else {
    console.error('Product details section not found');
  }

  initializePage();
  animateProductCards();
  setupSizeSelection();

  // Check if there's a product ID in the URL and show that product
  const urlParams = new URLSearchParams(window.location.search);
  const productId = urlParams.get('product');
  if (productId) {
    showProductDetails(productId);
  }

  // Add event delegation for product card clicks (backup method)
  document.addEventListener('click', (e) => {
    const productCard = e.target.closest('.product-card');
    if (productCard) {
      e.preventDefault();
      const productId = productCard.dataset.productId;
      if (productId) {
        console.log('Event delegation: Product card clicked, showing details for ID:', productId);
        showProductDetails(productId);
      }
    }
  });

  // Note: User icon click event is now handled in auth-ui.js
});
// Navbar scroll feature
function navBarScrollAnimation() {
  let lastScrollTop = 0;
  window.addEventListener("scroll", function() {
    let navbar = document.querySelector("nav");
    let currentScroll = window.pageYOffset;
    if (currentScroll > lastScrollTop && currentScroll > 100) {
      navbar.style.top = "-90px"; // Hide navbar
    } else {
      navbar.style.top = "0"; // Show navbar
    }
    lastScrollTop = currentScroll;
  });
}
navBarScrollAnimation();